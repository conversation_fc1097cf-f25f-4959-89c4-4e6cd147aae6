"""
数据库初始化脚本
创建基本的数据库表结构
"""

import asyncio
from sqlalchemy import create_engine, text
from app.core.config import settings

# 创建同步引擎用于初始化
engine = create_engine(str(settings.DATABASE_URL))

def create_basic_tables():
    """创建基本的数据库表结构"""
    
    # 用户表
    users_sql = """
    CREATE TABLE IF NOT EXISTS users (
        user_id BIGSERIAL PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role VARCHAR(16) NOT NULL CHECK (role IN ('admin', 'annotator', 'reviewer', 'viewer', 'engine')),
        full_name TEXT,
        email TEXT UNIQUE,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        last_login TIMESTAMPTZ,
        login_count INTEGER DEFAULT 0 NOT NULL,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
    """
    
    # 知识点表
    knowledge_points_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_points (
        kp_id SERIAL PRIMARY KEY,
        parent_id INTEGER REFERENCES knowledge_points(kp_id),
        path TEXT NOT NULL,
        name TEXT NOT NULL,
        code TEXT UNIQUE NOT NULL,
        description TEXT,
        is_leaf BOOLEAN DEFAULT FALSE NOT NULL,
        difficulty_level SMALLINT CHECK (difficulty_level IS NULL OR (difficulty_level >= 1 AND difficulty_level <= 5)),
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS idx_kp_parent_id ON knowledge_points(parent_id);
    CREATE INDEX IF NOT EXISTS idx_kp_code ON knowledge_points(code);
    CREATE INDEX IF NOT EXISTS idx_kp_path ON knowledge_points USING GIST(path);
    CREATE INDEX IF NOT EXISTS idx_kp_is_leaf ON knowledge_points(is_leaf);
    """
    
    # 题目表
    questions_sql = """
    CREATE TABLE IF NOT EXISTS questions (
        question_id BIGSERIAL PRIMARY KEY,
        content JSONB NOT NULL,
        q_type SMALLINT NOT NULL CHECK (q_type IN (0,1,2,3,4,5,6,7,8,9,10)),
        difficulty_lvl SMALLINT CHECK (difficulty_lvl IS NULL OR (difficulty_lvl >= 1 AND difficulty_lvl <= 5)),
        irt_ready BOOLEAN DEFAULT FALSE NOT NULL,
        answer_key JSONB,
        analysis TEXT,
        source TEXT,
        tags TEXT[],
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS idx_questions_q_type ON questions(q_type);
    CREATE INDEX IF NOT EXISTS idx_questions_difficulty_lvl ON questions(difficulty_lvl);
    CREATE INDEX IF NOT EXISTS idx_questions_irt_ready ON questions(irt_ready);
    CREATE INDEX IF NOT EXISTS idx_questions_is_active ON questions(is_active);
    CREATE INDEX IF NOT EXISTS idx_questions_tags ON questions USING GIN(tags);
    CREATE INDEX IF NOT EXISTS idx_questions_content ON questions USING GIN(content);
    """
    
    # 题目-知识点映射表
    item_kp_map_sql = """
    CREATE TABLE IF NOT EXISTS item_kp_map (
        question_id BIGINT REFERENCES questions(question_id),
        kp_id INTEGER REFERENCES knowledge_points(kp_id),
        is_required BOOLEAN DEFAULT TRUE NOT NULL,
        weight REAL DEFAULT 1.0 NOT NULL CHECK (weight >= 0 AND weight <= 1),
        confidence REAL DEFAULT 1.0 NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
        source SMALLINT DEFAULT 0 NOT NULL CHECK (source IN (0, 1, 2)),
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT,
        PRIMARY KEY (question_id, kp_id)
    );
    
    CREATE INDEX IF NOT EXISTS idx_item_kp_map_question_id ON item_kp_map(question_id);
    CREATE INDEX IF NOT EXISTS idx_item_kp_map_kp_id ON item_kp_map(kp_id);
    CREATE INDEX IF NOT EXISTS idx_item_kp_map_is_required ON item_kp_map(is_required);
    """
    
    # 先修关系表
    prerequisite_relation_sql = """
    CREATE TABLE IF NOT EXISTS prerequisite_relation (
        pre_kp_id INTEGER REFERENCES knowledge_points(kp_id),
        post_kp_id INTEGER REFERENCES knowledge_points(kp_id),
        source SMALLINT DEFAULT 0 NOT NULL CHECK (source IN (0, 1, 2)),
        confidence REAL DEFAULT 1.0 NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT,
        PRIMARY KEY (pre_kp_id, post_kp_id),
        CHECK (pre_kp_id != post_kp_id)
    );
    
    CREATE INDEX IF NOT EXISTS idx_prereq_pre ON prerequisite_relation(pre_kp_id);
    CREATE INDEX IF NOT EXISTS idx_prereq_post ON prerequisite_relation(post_kp_id);
    """
    
    # 标注任务表
    annotation_tasks_sql = """
    CREATE TABLE IF NOT EXISTS annotation_tasks (
        task_id BIGSERIAL PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        task_type SMALLINT NOT NULL CHECK (task_type IN (0,1,2,3,4)),
        status SMALLINT DEFAULT 0 NOT NULL CHECK (status IN (0,1,2,3,4,5)),
        priority SMALLINT DEFAULT 3 NOT NULL CHECK (priority >= 1 AND priority <= 5),
        assigned_to BIGINT REFERENCES users(user_id),
        reviewer_id BIGINT REFERENCES users(user_id),
        due_date TIMESTAMPTZ,
        started_at TIMESTAMPTZ,
        completed_at TIMESTAMPTZ,
        reviewed_at TIMESTAMPTZ,
        task_data JSONB,
        result_data JSONB,
        progress SMALLINT DEFAULT 0 NOT NULL CHECK (progress >= 0 AND progress <= 100),
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS idx_annotation_tasks_task_type ON annotation_tasks(task_type);
    CREATE INDEX IF NOT EXISTS idx_annotation_tasks_status ON annotation_tasks(status);
    CREATE INDEX IF NOT EXISTS idx_annotation_tasks_assigned_to ON annotation_tasks(assigned_to);
    """
    
    with engine.connect() as conn:
        print("创建用户表...")
        conn.execute(text(users_sql))
        
        print("创建知识点表...")
        conn.execute(text(knowledge_points_sql))
        
        print("创建题目表...")
        conn.execute(text(questions_sql))
        
        print("创建题目-知识点映射表...")
        conn.execute(text(item_kp_map_sql))
        
        print("创建先修关系表...")
        conn.execute(text(prerequisite_relation_sql))
        
        print("创建标注任务表...")
        conn.execute(text(annotation_tasks_sql))
        
        conn.commit()
        print("所有表创建成功！")

if __name__ == "__main__":
    create_basic_tables()
